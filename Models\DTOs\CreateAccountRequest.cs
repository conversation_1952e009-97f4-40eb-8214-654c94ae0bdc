using System.ComponentModel.DataAnnotations;

namespace Ez_Account_API.Models.DTOs
{
    public class CreateAccountRequest
    {
        [Required]
        [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        public string? Description { get; set; }
    }
}
