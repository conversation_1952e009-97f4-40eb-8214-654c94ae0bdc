using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Ez_Account_API.Data;
using Ez_Account_API.Models;
using Ez_Account_API.Models.DTOs;
using Ez_Account_API.Services;

namespace Ez_Account_API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UsersController : ControllerBase
    {
        private readonly EzAccountDbContext _context;
        private readonly IPasswordService _passwordService;

        public UsersController(EzAccountDbContext context, IPasswordService passwordService)
        {
            _context = context;
            _passwordService = passwordService;
        }

        // POST: api/Users/<USER>
        [HttpPost("register")]
        public async Task<ActionResult<RegisterResponse>> Register(RegisterRequest request)
        {
            try
            {
                // Check if user already exists
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == request.Email.ToLower());

                if (existingUser != null)
                {
                    return Ok(new RegisterResponse
                    {
                        Success = false,
                        Message = "User with this email already exists"
                    });
                }

                // Hash the password using Argon2
                string hashedPassword = _passwordService.HashPassword(request.Password);

                // Create new user
                var user = new User
                {
                    Id = Guid.NewGuid(),
                    Email = request.Email.ToLower().Trim(),
                    Password = hashedPassword,
                    CreatedAt = DateTime.UtcNow
                };

                // Add user to database
                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                return Ok(new RegisterResponse
                {
                    Success = true,
                    Message = "User registered successfully",
                    UserId = user.Id
                });
            }
            catch (Exception)
            {
                // Log the exception (you might want to use a proper logging framework)
                return Ok(new RegisterResponse
                {
                    Success = false,
                    Message = "An error occurred during registration. Please try again."
                });
            }
        }

        // GET: api/Users/<USER>
        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetUser(Guid id)
        {
            var user = await _context.Users.FindAsync(id);

            if (user == null)
            {
                return NotFound(new { message = "User not found" });
            }

            // Return user without password
            return Ok(new
            {
                Id = user.Id,
                Email = user.Email,
                CreatedAt = user.CreatedAt
            });
        }

        // GET: api/Users (for testing purposes - returns all users without passwords)
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetUsers()
        {
            var users = await _context.Users
                .Select(u => new
                {
                    Id = u.Id,
                    Email = u.Email,
                    CreatedAt = u.CreatedAt
                })
                .ToListAsync();

            return Ok(users);
        }
    }
}
