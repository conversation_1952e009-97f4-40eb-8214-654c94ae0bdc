using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Ez_Account_API.Data;
using Ez_Account_API.Models;
using Ez_Account_API.Models.DTOs;
using Ez_Account_API.Services;

namespace Ez_Account_API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UsersController : ControllerBase
    {
        private readonly EzAccountDbContext _context;
        private readonly IPasswordService _passwordService;
        private readonly IJwtService _jwtService;

        public UsersController(EzAccountDbContext context, IPasswordService passwordService, IJwtService jwtService)
        {
            _context = context;
            _passwordService = passwordService;
            _jwtService = jwtService;
        }

        // POST: api/Users/<USER>
        [HttpPost("register")]
        public async Task<ActionResult<RegisterResponse>> Register(RegisterRequest request)
        {
            try
            {
                // Check if user already exists
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == request.Email.ToLower());

                if (existingUser != null)
                {
                    return Ok(new RegisterResponse
                    {
                        Success = false,
                        Message = "User with this email already exists"
                    });
                }

                // Hash the password using Argon2
                string hashedPassword = _passwordService.HashPassword(request.Password);

                // Create new user
                var user = new User
                {
                    Id = Guid.NewGuid(),
                    Email = request.Email.ToLower().Trim(),
                    Password = hashedPassword,
                    CreatedAt = DateTime.UtcNow
                };

                // Add user to database
                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                return Ok(new RegisterResponse
                {
                    Success = true,
                    Message = "User registered successfully",
                    UserId = user.Id
                });
            }
            catch (Exception)
            {
                // Log the exception (you might want to use a proper logging framework)
                return Ok(new RegisterResponse
                {
                    Success = false,
                    Message = "An error occurred during registration. Please try again."
                });
            }
        }

        // POST: api/Users/<USER>
        [HttpPost("login")]
        public async Task<ActionResult<LoginResponse>> Login(LoginRequest request)
        {
            try
            {
                // Find user by email
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == request.Email.ToLower());

                if (user == null || !_passwordService.VerifyPassword(request.Password, user.Password))
                {
                    return Unauthorized(new { message = "Invalid email or password" });
                }

                // Generate tokens
                var loginResponse = _jwtService.CreateLoginResponse(user);

                // Update user with new refresh token
                user.RefreshToken = loginResponse.RefreshToken;
                user.RefreshTokenExpiry = loginResponse.RefreshTokenExpiration;
                await _context.SaveChangesAsync();

                return Ok(loginResponse);
            }
            catch (Exception)
            {
                return StatusCode(500, new { message = "An error occurred during login. Please try again." });
            }
        }
    }
}
