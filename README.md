# Ez-Account API

A simple accounting API built with ASP.NET Core and Entity Framework Core, connected to Microsoft SQL Server.

## Features

- **Account Management**: Create, read, update, and delete accounts
- **Transaction Tracking**: Record and manage financial transactions
- **Entity Framework Core**: Code-first approach with migrations
- **SQL Server Integration**: Full database connectivity
- **RESTful API**: Clean API endpoints with Swagger documentation

## Database Models

### Account
- Id (Primary Key)
- Name (Required, max 100 chars)
- AccountNumber (Unique, max 20 chars)
- Type (Asset, Liability, Equity, Revenue, Expense)
- Balance (Decimal)
- CreatedDate, UpdatedDate
- Description (Optional, max 500 chars)
- IsActive (Boolean)

### Transaction
- Id (Primary Key)
- Description (Required, max 200 chars)
- Amount (Decimal)
- TransactionDate
- CreatedDate
- Reference (Optional, max 50 chars)
- Type (Debit, Credit)
- AccountId (Foreign Key)

## Getting Started

### Prerequisites
- .NET 8 SDK
- SQL Server (LocalDB, SQL Server Express, or full SQL Server)
- Visual Studio 2022 or VS Code

### Setup Instructions

1. **Clone the repository** (if applicable)
2. **Install dependencies**:
   ```bash
   dotnet restore
   ```

3. **Update connection string** in `appsettings.json`:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=EzAccountDB;Trusted_Connection=true;MultipleActiveResultSets=true"
     }
   }
   ```

4. **Install Entity Framework tools** (if not already installed):
   ```bash
   dotnet tool install --global dotnet-ef
   ```

5. **Create and apply migrations**:
   ```bash
   dotnet ef migrations add InitialCreate
   dotnet ef database update
   ```

6. **Run the application**:
   ```bash
   dotnet run
   ```

7. **Access Swagger UI**: Navigate to `http://localhost:5145/swagger`

## API Endpoints

### Accounts
- `GET /api/accounts` - Get all active accounts
- `GET /api/accounts/{id}` - Get account by ID (includes transactions)
- `POST /api/accounts` - Create new account
- `PUT /api/accounts/{id}` - Update account
- `DELETE /api/accounts/{id}` - Soft delete account (marks as inactive)

### Sample Account JSON
```json
{
  "name": "Business Checking",
  "accountNumber": "1001",
  "type": 1,
  "balance": 5000.00,
  "description": "Main business checking account"
}
```

## Connection String Options

### LocalDB (Default)
```
Server=(localdb)\\mssqllocaldb;Database=EzAccountDB;Trusted_Connection=true;MultipleActiveResultSets=true
```

### SQL Server Express
```
Server=.\\SQLEXPRESS;Database=EzAccountDB;Trusted_Connection=true;MultipleActiveResultSets=true
```

### SQL Server with Authentication
```
Server=your-server;Database=EzAccountDB;User Id=your-username;Password=your-password;MultipleActiveResultSets=true
```

### Azure SQL Database
```
Server=tcp:your-server.database.windows.net,1433;Database=EzAccountDB;User ID=your-username;Password=your-password;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;
```

## Development Notes

- The application uses **Code First** approach with Entity Framework
- **Soft deletes** are implemented for accounts (IsActive flag)
- **Seed data** includes three default accounts (Cash, Accounts Receivable, Accounts Payable)
- **Database indexes** are configured for better performance
- **Foreign key constraints** maintain data integrity

## Next Steps

Consider adding:
- Authentication and authorization
- Transaction controller and endpoints
- Data validation and error handling
- Logging and monitoring
- Unit and integration tests
- Docker containerization
