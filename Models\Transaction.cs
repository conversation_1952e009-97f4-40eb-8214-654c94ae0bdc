using System.ComponentModel.DataAnnotations;

namespace Ez_Account_API.Models
{
    public class Transaction
    {
        public string Id { get; set; } = string.Empty;
        
        [Required]
        public double Amount { get; set; }
        
        [Required]
        public TransactionType Type { get; set; }
        
        public string? Description { get; set; }
        
        public DateTime Date { get; set; } = DateTime.UtcNow;
        
        [Required]
        public Guid UserId { get; set; }
        
        public string? FromAccountId { get; set; }
        
        public string? ToAccountId { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual Account? FromAccount { get; set; }
        public virtual Account? ToAccount { get; set; }
    }
    
    public enum TransactionType
    {
        Income,
        Expense,
        Transfer
    }
}
