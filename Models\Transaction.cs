using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ez_Account_API.Models
{
    public class Transaction
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Description { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        
        public DateTime TransactionDate { get; set; } = DateTime.UtcNow;
        
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        [StringLength(50)]
        public string? Reference { get; set; }
        
        public TransactionType Type { get; set; }
        
        // Foreign key
        public int AccountId { get; set; }
        
        // Navigation property
        public virtual Account Account { get; set; } = null!;
    }
    
    public enum TransactionType
    {
        Debit = 1,
        Credit = 2
    }
}
