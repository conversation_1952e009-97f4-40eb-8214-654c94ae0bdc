using System.ComponentModel.DataAnnotations;

namespace Ez_Account_API.Models
{
    public class TransactionType
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(10)]
        public string Code { get; set; } = string.Empty; // RCP, PAY, SALE, etc.
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string? Description { get; set; }
        
        // Navigation property
        public virtual ICollection<DayBookEntry> DayBookEntries { get; set; } = new List<DayBookEntry>();
    }
}
