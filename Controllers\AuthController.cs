using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Ez_Account_API.Data;
using Ez_Account_API.Models;
using Ez_Account_API.Models.DTOs;
using Ez_Account_API.Services;
using System.Security.Claims;

namespace Ez_Account_API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly EzAccountDbContext _context;
        private readonly IJwtService _jwtService;
        private readonly IPasswordService _passwordService;

        public AuthController(EzAccountDbContext context, IJwtService jwtService, IPasswordService passwordService)
        {
            _context = context;
            _jwtService = jwtService;
            _passwordService = passwordService;
        }

        // POST: api/Auth/login
        [HttpPost("login")]
        public async Task<ActionResult<LoginResponse>> Login(LoginRequest request)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Username == request.Username);

            if (user == null || !_passwordService.VerifyPassword(request.Password, user.PasswordHash))
            {
                return Unauthorized(new { message = "Invalid username or password" });
            }

            var loginResponse = _jwtService.CreateLoginResponse(user);

            // Update user with new refresh token
            user.RefreshToken = loginResponse.RefreshToken;
            user.RefreshTokenExpiry = loginResponse.RefreshTokenExpiration;
            await _context.SaveChangesAsync();

            return Ok(loginResponse);
        }

        // POST: api/Auth/refresh
        [HttpPost("refresh")]
        public async Task<ActionResult<LoginResponse>> RefreshToken(RefreshTokenRequest request)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.RefreshToken == request.RefreshToken);

            if (user == null || !_jwtService.ValidateRefreshToken(request.RefreshToken, user))
            {
                return Unauthorized(new { message = "Invalid refresh token" });
            }

            var loginResponse = _jwtService.CreateLoginResponse(user);

            // Update user with new refresh token
            user.RefreshToken = loginResponse.RefreshToken;
            user.RefreshTokenExpiry = loginResponse.RefreshTokenExpiration;
            await _context.SaveChangesAsync();

            return Ok(loginResponse);
        }

        // POST: api/Auth/logout
        [HttpPost("logout")]
        public async Task<IActionResult> Logout(RefreshTokenRequest request)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.RefreshToken == request.RefreshToken);

            if (user != null)
            {
                user.RefreshToken = null;
                user.RefreshTokenExpiry = null;
                await _context.SaveChangesAsync();
            }

            return Ok(new { message = "Logged out successfully" });
        }

        // POST: api/Auth/register (for creating new users)
        [HttpPost("register")]
        public async Task<ActionResult<User>> Register(LoginRequest request)
        {
            // Check if user already exists
            if (await _context.Users.AnyAsync(u => u.Username == request.Username))
            {
                return BadRequest(new { message = "Username already exists" });
            }

            var user = new User
            {
                Username = request.Username,
                PasswordHash = _passwordService.HashPassword(request.Password),
                CreatedAt = DateTime.UtcNow
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Remove password hash from response
            user.PasswordHash = string.Empty;
            return CreatedAtAction(nameof(Register), new { id = user.Id }, user);
        }

        // GET: api/Auth/me (protected endpoint to test JWT)
        [HttpGet("me")]
        [Authorize]
        public async Task<ActionResult<object>> GetCurrentUser()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var username = User.FindFirst(ClaimTypes.Name)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            var user = await _context.Users.FindAsync(int.Parse(userId));
            if (user == null)
            {
                return NotFound();
            }

            return Ok(new
            {
                Id = user.Id,
                Username = user.Username,
                CreatedAt = user.CreatedAt,
                Claims = User.Claims.Select(c => new { c.Type, c.Value })
            });
        }
    }
}
