using System.ComponentModel.DataAnnotations;

namespace Ez_Account_API.Models
{
    public class Partner
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? Contact { get; set; }
        
        [StringLength(100)]
        public string? Email { get; set; }
        
        [Required]
        public int LinkedAccountId { get; set; }
        
        // Navigation property
        public virtual Account LinkedAccount { get; set; } = null!;
    }
}
