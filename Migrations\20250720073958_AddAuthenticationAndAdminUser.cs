﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Ez_Account_API.Migrations
{
    /// <inheritdoc />
    public partial class AddAuthenticationAndAdminUser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "Users",
                columns: new[] { "Id", "CreatedAt", "PasswordHash", "RefreshToken", "RefreshTokenExpiry", "Username" },
                values: new object[] { 1, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "cTwwhhkzi3F2OR4xvLPytVy69Mw51D4g9PjiadbX7xU=", null, null, "Admin" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1);
        }
    }
}
