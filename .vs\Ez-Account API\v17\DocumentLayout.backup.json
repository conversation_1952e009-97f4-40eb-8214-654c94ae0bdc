{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\Ez-Accountant\\API\\Ez-Account API\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C5011C3D-0275-4F15-B10C-7F285D082028}|Ez-Account API.csproj|d:\\projects\\ez-accountant\\api\\ez-account api\\controllers\\weatherforecastcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C5011C3D-0275-4F15-B10C-7F285D082028}|Ez-Account API.csproj|solutionrelative:controllers\\weatherforecastcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "WeatherForecastController.cs", "DocumentMoniker": "D:\\Projects\\Ez-Accountant\\API\\Ez-Account API\\Controllers\\WeatherForecastController.cs", "RelativeDocumentMoniker": "Controllers\\WeatherForecastController.cs", "ToolTip": "D:\\Projects\\Ez-Accountant\\API\\Ez-Account API\\Controllers\\WeatherForecastController.cs", "RelativeToolTip": "Controllers\\WeatherForecastController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T12:40:16.054Z", "EditorCaption": ""}]}]}]}