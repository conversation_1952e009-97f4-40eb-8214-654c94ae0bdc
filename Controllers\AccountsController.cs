using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using Ez_Account_API.Data;
using Ez_Account_API.Models;
using Ez_Account_API.Models.DTOs;

namespace Ez_Account_API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AccountsController : ControllerBase
    {
        private readonly EzAccountDbContext _context;

        public AccountsController(EzAccountDbContext context)
        {
            _context = context;
        }

        // GET: api/Accounts
        [HttpGet]
        public async Task<ActionResult<IEnumerable<AccountResponse>>> GetAccounts()
        {
            try
            {
                // Get current user ID from JWT token
                var userIdClaim = HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
                {
                    return Unauthorized(new { message = "Invalid user token" });
                }

                // Get all accounts for the current user
                var accounts = await _context.Accounts
                    .Where(a => a.UserId == userId)
                    .OrderBy(a => a.Name)
                    .Select(a => new AccountResponse
                    {
                        Id = a.Id,
                        Name = a.Name,
                        Description = a.Description,
                        Balance = a.Balance,
                        CreatedAt = a.CreatedAt,
                        UpdatedAt = a.UpdatedAt
                    })
                    .ToListAsync();

                return Ok(accounts);
            }
            catch (Exception ex)
            {
                // Log the exception (you might want to use a proper logging framework)
                return StatusCode(500, new { message = "An error occurred while retrieving accounts. Please try again." });
            }
        }

        // POST: api/Accounts
        [HttpPost]
        public async Task<ActionResult<CreateAccountResponse>> CreateAccount(CreateAccountRequest request)
        {
            try
            {
                // Validate request
                if (!ModelState.IsValid)
                {
                    return BadRequest(new CreateAccountResponse
                    {
                        Success = false,
                        Message = "Invalid request data",
                    });
                }

                // Get current user ID from JWT token
                var userIdClaim = HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
                {
                    return Unauthorized(new { message = "Invalid user token" });
                }

                // Check if account with same name already exists for this user
                var existingAccount = await _context.Accounts
                    .FirstOrDefaultAsync(a => a.UserId == userId && a.Name.ToLower() == request.Name.ToLower());

                if (existingAccount != null)
                {
                    return Ok(new CreateAccountResponse
                    {
                        Success = false,
                        Message = "An account with this name already exists"
                    });
                }

                // Generate unique account ID
                var accountId = Guid.NewGuid().ToString();

                // Create new account
                var account = new Account
                {
                    Id = accountId,
                    Name = request.Name.Trim(),
                    Description = request.Description?.Trim(),
                    Balance = 0, // New accounts start with zero balance
                    UserId = userId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                // Add account to database
                _context.Accounts.Add(account);
                await _context.SaveChangesAsync();

                // Create response
                var accountResponse = new AccountResponse
                {
                    Id = account.Id,
                    Name = account.Name,
                    Description = account.Description,
                    Balance = account.Balance,
                    CreatedAt = account.CreatedAt,
                    UpdatedAt = account.UpdatedAt
                };

                return Ok(new CreateAccountResponse
                {
                    Success = true,
                    Message = "Account created successfully",
                    AccountId = account.Id,
                    Account = accountResponse
                });
            }
            catch (Exception ex)
            {
                // Log the exception (you might want to use a proper logging framework)
                return StatusCode(500, new CreateAccountResponse
                {
                    Success = false,
                    Message = "An error occurred while creating the account. Please try again."
                });
            }
        }
    }
}
