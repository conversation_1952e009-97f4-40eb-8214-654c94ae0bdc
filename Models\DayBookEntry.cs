using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ez_Account_API.Models
{
    public class DayBookEntry
    {
        public int Id { get; set; }
        
        [Required]
        public DateTime TransactionDate { get; set; }
        
        [Required]
        public int TransactionTypeId { get; set; }
        
        [StringLength(255)]
        public string? Description { get; set; }
        
        [Required]
        public int DebitAccountId { get; set; }
        
        [Required]
        public int CreditAccountId { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        
        [StringLength(50)]
        public string? ReferenceNo { get; set; }
        
        [StringLength(20)]
        public string Status { get; set; } = "Posted";
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual TransactionType TransactionType { get; set; } = null!;
        public virtual Account DebitAccount { get; set; } = null!;
        public virtual Account CreditAccount { get; set; } = null!;
    }
}
