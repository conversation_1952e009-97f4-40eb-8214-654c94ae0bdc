{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=ez_manager_dev;Username=postgres;Password=MyStrongP@ssword123;Port=5432"}, "JwtSettings": {"SecretKey": "MyVeryLongSecretKeyForJWTTokenGeneration123456789", "Issuer": "EzAccountAPI", "Audience": "EzAccountUsers", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}