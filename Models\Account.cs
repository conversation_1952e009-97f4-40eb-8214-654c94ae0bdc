using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Ez_Account_API.Models
{
    public class Account
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Type { get; set; } = string.Empty; // Cash, Bank, Capital, Debtor, Creditor
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0.00m;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        // Navigation properties
        public virtual ICollection<DayBookEntry> DebitEntries { get; set; } = new List<DayBookEntry>();
        public virtual ICollection<DayBookEntry> CreditEntries { get; set; } = new List<DayBookEntry>();
        public virtual ICollection<Partner> Partners { get; set; } = new List<Partner>();
    }
}
