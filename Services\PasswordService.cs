using Konscious.Security.Cryptography;
using System.Security.Cryptography;
using System.Text;

namespace Ez_Account_API.Services
{
    public class PasswordService : IPasswordService
    {
        private const int SaltSize = 16; // 128 bits
        private const int HashSize = 32; // 256 bits
        private const int Iterations = 4; // Number of iterations
        private const int MemorySize = 1024 * 1024; // 1 GB
        private const int Parallelism = 8; // Number of threads

        public string HashPassword(string password)
        {
            // Generate a random salt
            byte[] salt = new byte[SaltSize];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(salt);
            }

            // Hash the password using Argon2id
            using var argon2 = new Argon2id(Encoding.UTF8.GetBytes(password))
            {
                Salt = salt,
                DegreeOfParallelism = Parallelism,
                Iterations = Iterations,
                MemorySize = MemorySize
            };

            byte[] hash = argon2.GetBytes(HashSize);

            // Combine salt and hash for storage
            byte[] hashBytes = new byte[SaltSize + HashSize];
            Array.Copy(salt, 0, hashBytes, 0, SaltSize);
            Array.Copy(hash, 0, hashBytes, SaltSize, HashSize);

            // Convert to base64 for storage
            return Convert.ToBase64String(hashBytes);
        }

        public bool VerifyPassword(string password, string hashedPassword)
        {
            try
            {
                // Convert from base64
                byte[] hashBytes = Convert.FromBase64String(hashedPassword);

                // Extract salt and hash
                byte[] salt = new byte[SaltSize];
                byte[] hash = new byte[HashSize];
                Array.Copy(hashBytes, 0, salt, 0, SaltSize);
                Array.Copy(hashBytes, SaltSize, hash, 0, HashSize);

                // Hash the provided password with the same salt
                using var argon2 = new Argon2id(Encoding.UTF8.GetBytes(password))
                {
                    Salt = salt,
                    DegreeOfParallelism = Parallelism,
                    Iterations = Iterations,
                    MemorySize = MemorySize
                };

                byte[] testHash = argon2.GetBytes(HashSize);

                // Compare hashes using constant-time comparison
                return CryptographicOperations.FixedTimeEquals(hash, testHash);
            }
            catch
            {
                return false;
            }
        }
    }
}
