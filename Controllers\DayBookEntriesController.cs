using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Ez_Account_API.Data;
using Ez_Account_API.Models;

namespace Ez_Account_API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DayBookEntriesController : ControllerBase
    {
        private readonly EzAccountDbContext _context;

        public DayBookEntriesController(EzAccountDbContext context)
        {
            _context = context;
        }

        // GET: api/DayBookEntries
        [HttpGet]
        public async Task<ActionResult<IEnumerable<DayBookEntry>>> GetDayBookEntries()
        {
            return await _context.DayBookEntries
                .Include(d => d.TransactionType)
                .Include(d => d.DebitAccount)
                .Include(d => d.CreditAccount)
                .OrderByDescending(d => d.TransactionDate)
                .ThenByDescending(d => d.CreatedAt)
                .ToListAsync();
        }

        // GET: api/DayBookEntries/5
        [HttpGet("{id}")]
        public async Task<ActionResult<DayBookEntry>> GetDayBookEntry(int id)
        {
            var dayBookEntry = await _context.DayBookEntries
                .Include(d => d.TransactionType)
                .Include(d => d.DebitAccount)
                .Include(d => d.CreditAccount)
                .FirstOrDefaultAsync(d => d.Id == id);

            if (dayBookEntry == null)
            {
                return NotFound();
            }

            return dayBookEntry;
        }

        // GET: api/DayBookEntries/ByDate/{date}
        [HttpGet("ByDate/{date}")]
        public async Task<ActionResult<IEnumerable<DayBookEntry>>> GetDayBookEntriesByDate(DateTime date)
        {
            return await _context.DayBookEntries
                .Include(d => d.TransactionType)
                .Include(d => d.DebitAccount)
                .Include(d => d.CreditAccount)
                .Where(d => d.TransactionDate.Date == date.Date)
                .OrderBy(d => d.CreatedAt)
                .ToListAsync();
        }

        // GET: api/DayBookEntries/ByAccount/{accountId}
        [HttpGet("ByAccount/{accountId}")]
        public async Task<ActionResult<IEnumerable<DayBookEntry>>> GetDayBookEntriesByAccount(int accountId)
        {
            return await _context.DayBookEntries
                .Include(d => d.TransactionType)
                .Include(d => d.DebitAccount)
                .Include(d => d.CreditAccount)
                .Where(d => d.DebitAccountId == accountId || d.CreditAccountId == accountId)
                .OrderByDescending(d => d.TransactionDate)
                .ToListAsync();
        }

        // GET: api/DayBookEntries/ByTransactionType/{typeId}
        [HttpGet("ByTransactionType/{typeId}")]
        public async Task<ActionResult<IEnumerable<DayBookEntry>>> GetDayBookEntriesByTransactionType(int typeId)
        {
            return await _context.DayBookEntries
                .Include(d => d.TransactionType)
                .Include(d => d.DebitAccount)
                .Include(d => d.CreditAccount)
                .Where(d => d.TransactionTypeId == typeId)
                .OrderByDescending(d => d.TransactionDate)
                .ToListAsync();
        }

        // POST: api/DayBookEntries
        [HttpPost]
        public async Task<ActionResult<DayBookEntry>> PostDayBookEntry(DayBookEntry dayBookEntry)
        {
            _context.DayBookEntries.Add(dayBookEntry);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetDayBookEntry", new { id = dayBookEntry.Id }, dayBookEntry);
        }

        // PUT: api/DayBookEntries/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutDayBookEntry(int id, DayBookEntry dayBookEntry)
        {
            if (id != dayBookEntry.Id)
            {
                return BadRequest();
            }

            _context.Entry(dayBookEntry).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!DayBookEntryExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/DayBookEntries/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteDayBookEntry(int id)
        {
            var dayBookEntry = await _context.DayBookEntries.FindAsync(id);
            if (dayBookEntry == null)
            {
                return NotFound();
            }

            _context.DayBookEntries.Remove(dayBookEntry);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool DayBookEntryExists(int id)
        {
            return _context.DayBookEntries.Any(e => e.Id == id);
        }
    }
}
