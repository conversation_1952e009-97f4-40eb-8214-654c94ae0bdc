using Microsoft.EntityFrameworkCore;
using Ez_Account_API.Models;

namespace Ez_Account_API.Data
{
    public class EzAccountDbContext : DbContext
    {
        public EzAccountDbContext(DbContextOptions<EzAccountDbContext> options) : base(options)
        {
        }
        
        public DbSet<Account> Accounts { get; set; }
        public DbSet<Transaction> Transactions { get; set; }
        
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // Configure Account entity
            modelBuilder.Entity<Account>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.AccountNumber).HasMaxLength(20);
                entity.Property(e => e.Balance).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETUTCDATE()");
                
                // Index for better performance
                entity.HasIndex(e => e.AccountNumber).IsUnique();
                entity.HasIndex(e => e.Name);
            });
            
            // Configure Transaction entity
            modelBuilder.Entity<Transaction>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Description).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Reference).HasMaxLength(50);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETUTCDATE()");
                
                // Configure relationship
                entity.HasOne(t => t.Account)
                      .WithMany(a => a.Transactions)
                      .HasForeignKey(t => t.AccountId)
                      .OnDelete(DeleteBehavior.Restrict);
                
                // Index for better performance
                entity.HasIndex(e => e.AccountId);
                entity.HasIndex(e => e.TransactionDate);
            });
            
            // Seed data (optional)
            SeedData(modelBuilder);
        }
        
        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed some default accounts
            modelBuilder.Entity<Account>().HasData(
                new Account
                {
                    Id = 1,
                    Name = "Cash",
                    AccountNumber = "1001",
                    Type = AccountType.Asset,
                    Balance = 0,
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    Description = "Cash account for daily transactions",
                    IsActive = true
                },
                new Account
                {
                    Id = 2,
                    Name = "Accounts Receivable",
                    AccountNumber = "1200",
                    Type = AccountType.Asset,
                    Balance = 0,
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    Description = "Money owed by customers",
                    IsActive = true
                },
                new Account
                {
                    Id = 3,
                    Name = "Accounts Payable",
                    AccountNumber = "2001",
                    Type = AccountType.Liability,
                    Balance = 0,
                    CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                    Description = "Money owed to suppliers",
                    IsActive = true
                }
            );
        }
    }
}
