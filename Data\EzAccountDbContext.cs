using Microsoft.EntityFrameworkCore;
using Ez_Account_API.Models;

namespace Ez_Account_API.Data
{
    public class EzAccountDbContext : DbContext
    {
        public EzAccountDbContext(DbContextOptions<EzAccountDbContext> options) : base(options)
        {
        }
        
        public DbSet<User> Users { get; set; }
        public DbSet<Account> Accounts { get; set; }
        public DbSet<TransactionType> TransactionTypes { get; set; }
        public DbSet<DayBookEntry> DayBookEntries { get; set; }
        public DbSet<Partner> Partners { get; set; }
        
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
                entity.Property(e => e.PasswordHash).IsRequired().HasMaxLength(255);
                entity.Property(e => e.RefreshToken).HasMaxLength(500);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETDATE()");
                
                entity.HasIndex(e => e.Username).IsUnique();
            });
            
            // Configure Account entity
            modelBuilder.Entity<Account>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Type).IsRequired().HasMaxLength(50);
                entity.Property(e => e.OpeningBalance).HasColumnType("decimal(18,2)").HasDefaultValue(0.00m);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETDATE()");
            });
            
            // Configure TransactionType entity
            modelBuilder.Entity<TransactionType>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(10);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(200);
                
                entity.HasIndex(e => e.Code).IsUnique();
            });
            
            // Configure DayBookEntry entity
            modelBuilder.Entity<DayBookEntry>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TransactionDate).IsRequired();
                entity.Property(e => e.Description).HasMaxLength(255);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.ReferenceNo).HasMaxLength(50);
                entity.Property(e => e.Status).HasMaxLength(20).HasDefaultValue("Posted");
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETDATE()");
                
                // Configure relationships
                entity.HasOne(d => d.TransactionType)
                      .WithMany(p => p.DayBookEntries)
                      .HasForeignKey(d => d.TransactionTypeId)
                      .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(d => d.DebitAccount)
                      .WithMany(p => p.DebitEntries)
                      .HasForeignKey(d => d.DebitAccountId)
                      .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(d => d.CreditAccount)
                      .WithMany(p => p.CreditEntries)
                      .HasForeignKey(d => d.CreditAccountId)
                      .OnDelete(DeleteBehavior.Restrict);
                
                // Indexes
                entity.HasIndex(e => e.TransactionDate);
                entity.HasIndex(e => e.TransactionTypeId);
                entity.HasIndex(e => e.DebitAccountId);
                entity.HasIndex(e => e.CreditAccountId);
            });
            
            // Configure Partner entity
            modelBuilder.Entity<Partner>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Contact).HasMaxLength(50);
                entity.Property(e => e.Email).HasMaxLength(100);
                
                entity.HasOne(d => d.LinkedAccount)
                      .WithMany(p => p.Partners)
                      .HasForeignKey(d => d.LinkedAccountId)
                      .OnDelete(DeleteBehavior.Restrict);
            });
            
            // Seed Transaction Types
            SeedTransactionTypes(modelBuilder);

            // Seed Admin User
            SeedAdminUser(modelBuilder);
        }
        
        private void SeedTransactionTypes(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<TransactionType>().HasData(
                new TransactionType { Id = 1, Code = "RCP", Name = "Receipt", Description = "Cash/Bank received" },
                new TransactionType { Id = 2, Code = "PAY", Name = "Payment", Description = "Cash/Bank paid" },
                new TransactionType { Id = 3, Code = "SALE", Name = "Sales", Description = "Goods or services sold" },
                new TransactionType { Id = 4, Code = "PURCH", Name = "Purchase", Description = "Goods or services purchased" },
                new TransactionType { Id = 5, Code = "JOUR", Name = "Journal", Description = "Adjustment entries" },
                new TransactionType { Id = 6, Code = "TRF", Name = "Transfer", Description = "Cash/bank transfer" },
                new TransactionType { Id = 7, Code = "CAPIN", Name = "Capital In", Description = "Partner capital investment" },
                new TransactionType { Id = 8, Code = "CAPOUT", Name = "Capital Out", Description = "Capital withdrawal by partner" }
            );
        }

        private void SeedAdminUser(ModelBuilder modelBuilder)
        {
            // Hash the password "Password123!" using the same method as PasswordService
            var password = "Password123!";
            var salt = "EzAccountSalt";
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password + salt));
            var hashedPassword = Convert.ToBase64String(hashedBytes);

            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    Username = "Admin",
                    PasswordHash = hashedPassword,
                    CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                }
            );
        }
    }
}
