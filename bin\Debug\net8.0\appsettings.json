{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=EZ_manager;User Id=sa;Password=MyStrongP@ssword123;TrustServerCertificate=true;MultipleActiveResultSets=true"}, "JwtSettings": {"SecretKey": "MyVeryLongSecretKeyForJWTTokenGeneration123456789", "Issuer": "EzAccountAPI", "Audience": "EzAccountUsers", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}